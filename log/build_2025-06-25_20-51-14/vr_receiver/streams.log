[2.051s] Invoking command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/vr_receiver/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_receiver build --build-base /home/<USER>/ws_ros2/build/vr_receiver/build install --record /home/<USER>/ws_ros2/build/vr_receiver/install.log --single-version-externally-managed install_data
[2.340s] running egg_info
[2.362s] writing ../../../build/vr_receiver/vr_receiver.egg-info/PKG-INFO
[2.362s] writing dependency_links to ../../../build/vr_receiver/vr_receiver.egg-info/dependency_links.txt
[2.362s] writing entry points to ../../../build/vr_receiver/vr_receiver.egg-info/entry_points.txt
[2.362s] writing requirements to ../../../build/vr_receiver/vr_receiver.egg-info/requires.txt
[2.363s] writing top-level names to ../../../build/vr_receiver/vr_receiver.egg-info/top_level.txt
[2.407s] reading manifest file '../../../build/vr_receiver/vr_receiver.egg-info/SOURCES.txt'
[2.408s] writing manifest file '../../../build/vr_receiver/vr_receiver.egg-info/SOURCES.txt'
[2.408s] running build
[2.408s] running build_py
[2.408s] running install
[2.415s] running install_lib
[2.438s] running install_data
[2.438s] running install_egg_info
[2.461s] removing '/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages/vr_receiver-0.0.0-py3.12.egg-info' (and everything under it)
[2.461s] Copying ../../../build/vr_receiver/vr_receiver.egg-info to /home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages/vr_receiver-0.0.0-py3.12.egg-info
[2.462s] running install_scripts
[2.623s] Installing demo_vr_receiver script to /home/<USER>/ws_ros2/install/vr_receiver/lib/vr_receiver
[2.624s] Installing vr_receiver script to /home/<USER>/ws_ros2/install/vr_receiver/lib/vr_receiver
[2.624s] writing list of installed files to '/home/<USER>/ws_ros2/build/vr_receiver/install.log'
[2.662s] Invoked command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/vr_receiver/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_receiver build --build-base /home/<USER>/ws_ros2/build/vr_receiver/build install --record /home/<USER>/ws_ros2/build/vr_receiver/install.log --single-version-externally-managed install_data
