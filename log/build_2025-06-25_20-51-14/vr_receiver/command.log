Invoking command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/vr_receiver/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_receiver build --build-base /home/<USER>/ws_ros2/build/vr_receiver/build install --record /home/<USER>/ws_ros2/build/vr_receiver/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/vr_receiver/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_receiver build --build-base /home/<USER>/ws_ros2/build/vr_receiver/build install --record /home/<USER>/ws_ros2/build/vr_receiver/install.log --single-version-externally-managed install_data
