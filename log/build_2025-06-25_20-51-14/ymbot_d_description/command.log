Invoking command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/ymbot_d_description/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/ymbot_d_description build --build-base /home/<USER>/ws_ros2/build/ymbot_d_description/build install --record /home/<USER>/ws_ros2/build/ymbot_d_description/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/ymbot_d_description/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/ymbot_d_description build --build-base /home/<USER>/ws_ros2/build/ymbot_d_description/build install --record /home/<USER>/ws_ros2/build/ymbot_d_description/install.log --single-version-externally-managed install_data
