[1.895s] Invoking command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/ymbot_d_description/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/ymbot_d_description build --build-base /home/<USER>/ws_ros2/build/ymbot_d_description/build install --record /home/<USER>/ws_ros2/build/ymbot_d_description/install.log --single-version-externally-managed install_data
[2.345s] running egg_info
[2.370s] writing ../../../build/ymbot_d_description/ymbot_d_description.egg-info/PKG-INFO
[2.370s] writing dependency_links to ../../../build/ymbot_d_description/ymbot_d_description.egg-info/dependency_links.txt
[2.370s] writing requirements to ../../../build/ymbot_d_description/ymbot_d_description.egg-info/requires.txt
[2.370s] writing top-level names to ../../../build/ymbot_d_description/ymbot_d_description.egg-info/top_level.txt
[2.393s] reading manifest file '../../../build/ymbot_d_description/ymbot_d_description.egg-info/SOURCES.txt'
[2.395s] writing manifest file '../../../build/ymbot_d_description/ymbot_d_description.egg-info/SOURCES.txt'
[2.395s] running build
[2.395s] running install
[2.401s] running install_data
[2.402s] running install_egg_info
[2.460s] removing '/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages/ymbot_d_description-0.0.0-py3.12.egg-info' (and everything under it)
[2.460s] Copying ../../../build/ymbot_d_description/ymbot_d_description.egg-info to /home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages/ymbot_d_description-0.0.0-py3.12.egg-info
[2.461s] running install_scripts
[2.610s] writing list of installed files to '/home/<USER>/ws_ros2/build/ymbot_d_description/install.log'
[2.641s] Invoked command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/ymbot_d_description/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/ymbot_d_description build --build-base /home/<USER>/ws_ros2/build/ymbot_d_description/build install --record /home/<USER>/ws_ros2/build/ymbot_d_description/install.log --single-version-externally-managed install_data
