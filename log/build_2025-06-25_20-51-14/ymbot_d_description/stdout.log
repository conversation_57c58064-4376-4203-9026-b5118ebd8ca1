running egg_info
writing ../../../build/ymbot_d_description/ymbot_d_description.egg-info/PKG-INFO
writing dependency_links to ../../../build/ymbot_d_description/ymbot_d_description.egg-info/dependency_links.txt
writing requirements to ../../../build/ymbot_d_description/ymbot_d_description.egg-info/requires.txt
writing top-level names to ../../../build/ymbot_d_description/ymbot_d_description.egg-info/top_level.txt
reading manifest file '../../../build/ymbot_d_description/ymbot_d_description.egg-info/SOURCES.txt'
writing manifest file '../../../build/ymbot_d_description/ymbot_d_description.egg-info/SOURCES.txt'
running build
running install
running install_data
running install_egg_info
removing '/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages/ymbot_d_description-0.0.0-py3.12.egg-info' (and everything under it)
Copying ../../../build/ymbot_d_description/ymbot_d_description.egg-info to /home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages/ymbot_d_description-0.0.0-py3.12.egg-info
running install_scripts
writing list of installed files to '/home/<USER>/ws_ros2/build/ymbot_d_description/install.log'
