[0.822s] Invoking command in '/home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery -- -j20 -l20
[0.903s] [ 33%] Built target body_joint_tracking
[0.914s] [100%] Built target left_arm_pose_tracking
[0.914s] [100%] Built target right_arm_pose_tracking
[1.204s] Invoked command in '/home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery -- -j20 -l20
[1.206s] Invoking command in '/home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery
[1.244s] -- Install configuration: ""
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/lib/vr_pose_tracking_singularity_recovery/right_arm_pose_tracking
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/lib/vr_pose_tracking_singularity_recovery/left_arm_pose_tracking
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/lib/vr_pose_tracking_singularity_recovery/body_joint_tracking
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/launch
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/launch/singularity_recovery.launch.py
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/ament_index/resource_index/package_run_dependencies/vr_pose_tracking_singularity_recovery
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/ament_index/resource_index/parent_prefix_path/vr_pose_tracking_singularity_recovery
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/environment/ament_prefix_path.sh
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/environment/ament_prefix_path.dsv
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/environment/path.sh
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/environment/path.dsv
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/local_setup.bash
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/local_setup.sh
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/local_setup.zsh
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/local_setup.dsv
[1.245s] -- Installing: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.dsv
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/ament_index/resource_index/packages/vr_pose_tracking_singularity_recovery
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/cmake/vr_pose_tracking_singularity_recoveryConfig.cmake
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/cmake/vr_pose_tracking_singularity_recoveryConfig-version.cmake
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.xml
[1.355s] Invoked command in '/home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery
