[0.825s] Invoking command in '/home/<USER>/ws_ros2/build/udp2joint': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/udp2joint -- -j20 -l20
[0.889s] [ 50%] Built target dlltest
[0.909s] [100%] Built target moveit_test
[1.209s] Invoked command in '/home/<USER>/ws_ros2/build/udp2joint' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/udp2joint -- -j20 -l20
[1.210s] Invoking command in '/home/<USER>/ws_ros2/build/udp2joint': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/udp2joint
[1.240s] -- Install configuration: ""
[1.240s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/lib/udp2joint/dlltest
[1.241s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/lib/udp2joint/moveit_test
[1.241s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/launch
[1.242s] -- Installing: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/launch/run.launch.py
[1.242s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/launch/udptest.launch.py
[1.242s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/launch/lbs_run.launch.py
[1.242s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//config
[1.243s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//config/controllers.yaml
[1.243s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//rviz
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//rviz/rviz2.rviz
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//rviz/rviz.rviz
[1.244s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/ament_index/resource_index/package_run_dependencies/udp2joint
[1.245s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/ament_index/resource_index/parent_prefix_path/udp2joint
[1.246s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/environment/ament_prefix_path.sh
[1.247s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/environment/ament_prefix_path.dsv
[1.247s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/environment/path.sh
[1.248s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/environment/path.dsv
[1.248s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/local_setup.bash
[1.248s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/local_setup.sh
[1.248s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/local_setup.zsh
[1.248s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/local_setup.dsv
[1.248s] -- Installing: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.dsv
[1.248s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/ament_index/resource_index/packages/udp2joint
[1.248s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/cmake/udp2jointConfig.cmake
[1.248s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/cmake/udp2jointConfig-version.cmake
[1.248s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.xml
[1.345s] Invoked command in '/home/<USER>/ws_ros2/build/udp2joint' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/udp2joint
