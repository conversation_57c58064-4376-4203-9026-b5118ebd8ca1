[0.072s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.072s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=20, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0x7bc5e0fe5880>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7bc5e0fe5520>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7bc5e0fe5520>>, mixin_verb=('build',))
[0.093s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.093s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.093s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.093s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.093s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.093s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.093s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/ws_ros2'
[0.093s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.094s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.108s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extensions ['ignore', 'ignore_ament_install']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extension 'ignore'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extension 'ignore_ament_install'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extensions ['colcon_pkg']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extension 'colcon_pkg'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extensions ['colcon_meta']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extension 'colcon_meta'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extensions ['ros']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extension 'ros'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extensions ['cmake', 'python']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extension 'cmake'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extension 'python'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extensions ['python_setup_py']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(Log) by extension 'python_setup_py'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.109s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extension 'ignore_ament_install'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extensions ['colcon_pkg']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extension 'colcon_pkg'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extensions ['colcon_meta']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extension 'colcon_meta'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extensions ['ros']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extension 'ros'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extensions ['cmake', 'python']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extension 'cmake'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extension 'python'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extensions ['python_setup_py']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2) by extension 'python_setup_py'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extension 'ignore_ament_install'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extensions ['colcon_pkg']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extension 'colcon_pkg'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extensions ['colcon_meta']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extension 'colcon_meta'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extensions ['ros']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extension 'ros'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extensions ['cmake', 'python']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extension 'cmake'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extension 'python'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extensions ['python_setup_py']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2) by extension 'python_setup_py'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extensions ['ignore', 'ignore_ament_install']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extension 'ignore'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extension 'ignore_ament_install'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extensions ['colcon_pkg']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extension 'colcon_pkg'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extensions ['colcon_meta']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extension 'colcon_meta'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extensions ['ros']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extension 'ros'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extensions ['cmake', 'python']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extension 'cmake'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extension 'python'
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extensions ['python_setup_py']
[0.110s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs) by extension 'python_setup_py'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extensions ['ignore', 'ignore_ament_install']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extension 'ignore'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extension 'ignore_ament_install'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extensions ['colcon_pkg']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extension 'colcon_pkg'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extensions ['colcon_meta']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extension 'ros'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extensions ['cmake', 'python']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extension 'cmake'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extension 'python'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extensions ['python_setup_py']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/docs/images) by extension 'python_setup_py'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera) by extensions ['ignore', 'ignore_ament_install']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera) by extension 'ignore'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera) by extension 'ignore_ament_install'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera) by extensions ['colcon_pkg']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera) by extension 'colcon_pkg'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera) by extensions ['colcon_meta']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera) by extension 'ros'
[0.113s] DEBUG:colcon.colcon_core.package_identification:Package 'src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera' with type 'ros.ament_cmake' and name 'orbbec_camera'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs) by extension 'ignore'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs) by extension 'ignore_ament_install'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs) by extensions ['colcon_pkg']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs) by extension 'colcon_pkg'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs) by extensions ['colcon_meta']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs) by extension 'colcon_meta'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs) by extensions ['ros']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs) by extension 'ros'
[0.114s] DEBUG:colcon.colcon_core.package_identification:Package 'src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs' with type 'ros.ament_cmake' and name 'orbbec_camera_msgs'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description) by extensions ['ignore', 'ignore_ament_install']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description) by extension 'ignore'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description) by extension 'ignore_ament_install'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description) by extensions ['colcon_pkg']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description) by extension 'colcon_pkg'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description) by extensions ['colcon_meta']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description) by extension 'colcon_meta'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description) by extensions ['ros']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description) by extension 'ros'
[0.114s] DEBUG:colcon.colcon_core.package_identification:Package 'src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description' with type 'ros.ament_cmake' and name 'orbbec_description'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/inspire_hand) by extensions ['ignore', 'ignore_ament_install']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/inspire_hand) by extension 'ignore'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/inspire_hand) by extension 'ignore_ament_install'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/inspire_hand) by extensions ['colcon_pkg']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/inspire_hand) by extension 'colcon_pkg'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/inspire_hand) by extensions ['colcon_meta']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/inspire_hand) by extension 'colcon_meta'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/inspire_hand) by extensions ['ros']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/inspire_hand) by extension 'ros'
[0.114s] DEBUG:colcon.colcon_core.package_identification:Package 'src/Sensors_ROS2/inspire_hand' with type 'ros.ament_python' and name 'inspire_hand'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extensions ['ignore', 'ignore_ament_install']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extension 'ignore'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extension 'ignore_ament_install'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extensions ['colcon_pkg']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extension 'colcon_pkg'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extensions ['colcon_meta']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extension 'colcon_meta'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extensions ['ros']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extension 'ros'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extensions ['cmake', 'python']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extension 'cmake'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extension 'python'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extensions ['python_setup_py']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros) by extension 'python_setup_py'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera) by extensions ['ignore', 'ignore_ament_install']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera) by extension 'ignore'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera) by extension 'ignore_ament_install'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera) by extensions ['colcon_pkg']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera) by extension 'colcon_pkg'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera) by extensions ['colcon_meta']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera) by extension 'colcon_meta'
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera) by extensions ['ros']
[0.115s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera) by extension 'ros'
[0.118s] DEBUG:colcon.colcon_core.package_identification:Package 'src/Sensors_ROS2/realsense-ros/realsense2_camera' with type 'ros.ament_cmake' and name 'realsense2_camera'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs) by extensions ['ignore', 'ignore_ament_install']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs) by extension 'ignore'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs) by extension 'ignore_ament_install'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs) by extensions ['colcon_pkg']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs) by extension 'colcon_pkg'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs) by extensions ['colcon_meta']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs) by extension 'colcon_meta'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs) by extensions ['ros']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs) by extension 'ros'
[0.118s] DEBUG:colcon.colcon_core.package_identification:Package 'src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs' with type 'ros.ament_cmake' and name 'realsense2_camera_msgs'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_description) by extensions ['ignore', 'ignore_ament_install']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_description) by extension 'ignore'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_description) by extension 'ignore_ament_install'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_description) by extensions ['colcon_pkg']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_description) by extension 'colcon_pkg'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_description) by extensions ['colcon_meta']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_description) by extension 'colcon_meta'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_description) by extensions ['ros']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/realsense2_description) by extension 'ros'
[0.119s] DEBUG:colcon.colcon_core.package_identification:Package 'src/Sensors_ROS2/realsense-ros/realsense2_description' with type 'ros.ament_cmake' and name 'realsense2_description'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extensions ['ignore', 'ignore_ament_install']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extension 'ignore'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extension 'ignore_ament_install'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extensions ['colcon_pkg']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extension 'colcon_pkg'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extensions ['colcon_meta']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extension 'colcon_meta'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extensions ['ros']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extension 'ros'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extensions ['cmake', 'python']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extension 'cmake'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extension 'python'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extensions ['python_setup_py']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/Sensors_ROS2/realsense-ros/scripts) by extension 'python_setup_py'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extensions ['ignore', 'ignore_ament_install']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extension 'ignore'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extension 'ignore_ament_install'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extensions ['colcon_pkg']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extension 'colcon_pkg'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extensions ['colcon_meta']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extension 'colcon_meta'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extensions ['ros']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extension 'ros'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extensions ['cmake', 'python']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extension 'cmake'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extension 'python'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extensions ['python_setup_py']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl) by extension 'python_setup_py'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder) by extensions ['ignore', 'ignore_ament_install']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder) by extension 'ignore'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder) by extension 'ignore_ament_install'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder) by extensions ['colcon_pkg']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder) by extension 'colcon_pkg'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder) by extensions ['colcon_meta']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder) by extension 'colcon_meta'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder) by extensions ['ros']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder) by extension 'ros'
[0.120s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder' with type 'ros.ament_python' and name 'dataset_recorder'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint) by extensions ['ignore', 'ignore_ament_install']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint) by extension 'ignore'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint) by extension 'ignore_ament_install'
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint) by extensions ['colcon_pkg']
[0.120s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint) by extension 'colcon_pkg'
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint) by extensions ['colcon_meta']
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint) by extension 'colcon_meta'
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint) by extensions ['ros']
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint) by extension 'ros'
[0.121s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint' with type 'ros.ament_cmake' and name 'udp2joint'
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver) by extensions ['ignore', 'ignore_ament_install']
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver) by extension 'ignore'
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver) by extension 'ignore_ament_install'
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver) by extensions ['colcon_pkg']
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver) by extension 'colcon_pkg'
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver) by extensions ['colcon_meta']
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver) by extension 'colcon_meta'
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver) by extensions ['ros']
[0.121s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver) by extension 'ros'
[0.122s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver' with type 'ros.ament_python' and name 'vr_receiver'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control) by extensions ['ignore', 'ignore_ament_install']
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control) by extension 'ignore'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control) by extension 'ignore_ament_install'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control) by extensions ['colcon_pkg']
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control) by extension 'colcon_pkg'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control) by extensions ['colcon_meta']
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control) by extension 'colcon_meta'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control) by extensions ['ros']
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control) by extension 'ros'
[0.122s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control' with type 'ros.ament_cmake' and name 'ymbot_d_control'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description) by extensions ['ignore', 'ignore_ament_install']
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description) by extension 'ignore'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description) by extension 'ignore_ament_install'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description) by extensions ['colcon_pkg']
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description) by extension 'colcon_pkg'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description) by extensions ['colcon_meta']
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description) by extension 'colcon_meta'
[0.122s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description) by extensions ['ros']
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description) by extension 'ros'
[0.123s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description' with type 'ros.ament_python' and name 'ymbot_d_description'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config) by extensions ['ignore', 'ignore_ament_install']
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config) by extension 'ignore'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config) by extension 'ignore_ament_install'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config) by extensions ['colcon_pkg']
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config) by extension 'colcon_pkg'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config) by extensions ['colcon_meta']
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config) by extension 'colcon_meta'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config) by extensions ['ros']
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config) by extension 'ros'
[0.123s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config' with type 'ros.ament_cmake' and name 'ymbot_d_moveit_config'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller) by extensions ['ignore', 'ignore_ament_install']
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller) by extension 'ignore'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller) by extension 'ignore_ament_install'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller) by extensions ['colcon_pkg']
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller) by extension 'colcon_pkg'
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller) by extensions ['colcon_meta']
[0.123s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller) by extension 'colcon_meta'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller) by extensions ['ros']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller) by extension 'ros'
[0.124s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller' with type 'ros.ament_cmake' and name 'ymbot_d_ros2_controller'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver) by extensions ['ignore', 'ignore_ament_install']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver) by extension 'ignore'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver) by extension 'ignore_ament_install'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver) by extensions ['colcon_pkg']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver) by extension 'colcon_pkg'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver) by extensions ['colcon_meta']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver) by extension 'colcon_meta'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver) by extensions ['ros']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver) by extension 'ros'
[0.124s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver' with type 'ros.ament_cmake' and name 'ymbot_hardware_driver'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control) by extensions ['ignore', 'ignore_ament_install']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control) by extension 'ignore'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control) by extension 'ignore_ament_install'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control) by extensions ['colcon_pkg']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control) by extension 'colcon_pkg'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control) by extensions ['colcon_meta']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control) by extension 'colcon_meta'
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control) by extensions ['ros']
[0.124s] Level 1:colcon.colcon_core.package_identification:_identify(src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control) by extension 'ros'
[0.125s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control' with type 'ros.ament_cmake' and name 'vr_pose_tracking_singularity_recovery'
[0.125s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.125s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.125s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.125s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.125s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.138s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.138s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.140s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 16 installed packages in /home/<USER>/ws_ros2/install
[0.141s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 73 installed packages in /home/<USER>/ws_moveit/install
[0.142s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 418 installed packages in /opt/ros/jazzy
[0.143s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.180s] Level 5:colcon.colcon_core.verb:set package 'dataset_recorder' build argument 'cmake_args' from command line to 'None'
[0.180s] Level 5:colcon.colcon_core.verb:set package 'dataset_recorder' build argument 'cmake_target' from command line to 'None'
[0.180s] Level 5:colcon.colcon_core.verb:set package 'dataset_recorder' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.180s] Level 5:colcon.colcon_core.verb:set package 'dataset_recorder' build argument 'cmake_clean_cache' from command line to 'False'
[0.180s] Level 5:colcon.colcon_core.verb:set package 'dataset_recorder' build argument 'cmake_clean_first' from command line to 'False'
[0.180s] Level 5:colcon.colcon_core.verb:set package 'dataset_recorder' build argument 'cmake_force_configure' from command line to 'False'
[0.180s] Level 5:colcon.colcon_core.verb:set package 'dataset_recorder' build argument 'ament_cmake_args' from command line to 'None'
[0.180s] Level 5:colcon.colcon_core.verb:set package 'dataset_recorder' build argument 'catkin_cmake_args' from command line to 'None'
[0.180s] Level 5:colcon.colcon_core.verb:set package 'dataset_recorder' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.180s] DEBUG:colcon.colcon_core.verb:Building package 'dataset_recorder' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/dataset_recorder', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/dataset_recorder', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder', 'symlink_install': False, 'test_result_base': None}
[0.180s] Level 5:colcon.colcon_core.verb:set package 'inspire_hand' build argument 'cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'inspire_hand' build argument 'cmake_target' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'inspire_hand' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'inspire_hand' build argument 'cmake_clean_cache' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'inspire_hand' build argument 'cmake_clean_first' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'inspire_hand' build argument 'cmake_force_configure' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'inspire_hand' build argument 'ament_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'inspire_hand' build argument 'catkin_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'inspire_hand' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.181s] DEBUG:colcon.colcon_core.verb:Building package 'inspire_hand' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/inspire_hand', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/inspire_hand', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/Sensors_ROS2/inspire_hand', 'symlink_install': False, 'test_result_base': None}
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera_msgs' build argument 'cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera_msgs' build argument 'cmake_target' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.181s] DEBUG:colcon.colcon_core.verb:Building package 'orbbec_camera_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/orbbec_camera_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/orbbec_camera_msgs', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs', 'symlink_install': False, 'test_result_base': None}
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_description' build argument 'cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_description' build argument 'cmake_target' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_description' build argument 'cmake_clean_first' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_description' build argument 'cmake_force_configure' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_description' build argument 'ament_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'orbbec_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.181s] DEBUG:colcon.colcon_core.verb:Building package 'orbbec_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/orbbec_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/orbbec_description', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description', 'symlink_install': False, 'test_result_base': None}
[0.181s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera_msgs' build argument 'cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera_msgs' build argument 'cmake_target' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera_msgs' build argument 'cmake_clean_first' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera_msgs' build argument 'cmake_force_configure' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera_msgs' build argument 'ament_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.181s] DEBUG:colcon.colcon_core.verb:Building package 'realsense2_camera_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/realsense2_camera_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/realsense2_camera_msgs', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs', 'symlink_install': False, 'test_result_base': None}
[0.181s] Level 5:colcon.colcon_core.verb:set package 'udp2joint' build argument 'cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'udp2joint' build argument 'cmake_target' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'udp2joint' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'udp2joint' build argument 'cmake_clean_cache' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'udp2joint' build argument 'cmake_clean_first' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'udp2joint' build argument 'cmake_force_configure' from command line to 'False'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'udp2joint' build argument 'ament_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'udp2joint' build argument 'catkin_cmake_args' from command line to 'None'
[0.181s] Level 5:colcon.colcon_core.verb:set package 'udp2joint' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.181s] DEBUG:colcon.colcon_core.verb:Building package 'udp2joint' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/udp2joint', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/udp2joint', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint', 'symlink_install': False, 'test_result_base': None}
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_pose_tracking_singularity_recovery' build argument 'cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_pose_tracking_singularity_recovery' build argument 'cmake_target' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_pose_tracking_singularity_recovery' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_pose_tracking_singularity_recovery' build argument 'cmake_clean_cache' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_pose_tracking_singularity_recovery' build argument 'cmake_clean_first' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_pose_tracking_singularity_recovery' build argument 'cmake_force_configure' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_pose_tracking_singularity_recovery' build argument 'ament_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_pose_tracking_singularity_recovery' build argument 'catkin_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_pose_tracking_singularity_recovery' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.182s] DEBUG:colcon.colcon_core.verb:Building package 'vr_pose_tracking_singularity_recovery' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control', 'symlink_install': False, 'test_result_base': None}
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_receiver' build argument 'cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_receiver' build argument 'cmake_target' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_receiver' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_receiver' build argument 'cmake_clean_cache' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_receiver' build argument 'cmake_clean_first' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_receiver' build argument 'cmake_force_configure' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_receiver' build argument 'ament_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_receiver' build argument 'catkin_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'vr_receiver' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.182s] DEBUG:colcon.colcon_core.verb:Building package 'vr_receiver' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/vr_receiver', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/vr_receiver', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver', 'symlink_install': False, 'test_result_base': None}
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_description' build argument 'cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_description' build argument 'cmake_target' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_description' build argument 'cmake_clean_first' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_description' build argument 'cmake_force_configure' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_description' build argument 'ament_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.182s] DEBUG:colcon.colcon_core.verb:Building package 'ymbot_d_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/ymbot_d_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/ymbot_d_description', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description', 'symlink_install': False, 'test_result_base': None}
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_hardware_driver' build argument 'cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_hardware_driver' build argument 'cmake_target' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_hardware_driver' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_hardware_driver' build argument 'cmake_clean_cache' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_hardware_driver' build argument 'cmake_clean_first' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_hardware_driver' build argument 'cmake_force_configure' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_hardware_driver' build argument 'ament_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_hardware_driver' build argument 'catkin_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'ymbot_hardware_driver' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.182s] DEBUG:colcon.colcon_core.verb:Building package 'ymbot_hardware_driver' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/ymbot_hardware_driver', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/ymbot_hardware_driver', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver', 'symlink_install': False, 'test_result_base': None}
[0.182s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera' build argument 'cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera' build argument 'cmake_target' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera' build argument 'cmake_clean_cache' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera' build argument 'cmake_clean_first' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera' build argument 'cmake_force_configure' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera' build argument 'ament_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera' build argument 'catkin_cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'orbbec_camera' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.182s] DEBUG:colcon.colcon_core.verb:Building package 'orbbec_camera' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/orbbec_camera', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/orbbec_camera', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera', 'symlink_install': False, 'test_result_base': None}
[0.182s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera' build argument 'cmake_args' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera' build argument 'cmake_target' from command line to 'None'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera' build argument 'cmake_clean_cache' from command line to 'False'
[0.182s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera' build argument 'cmake_clean_first' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera' build argument 'cmake_force_configure' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera' build argument 'ament_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera' build argument 'catkin_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_camera' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.183s] DEBUG:colcon.colcon_core.verb:Building package 'realsense2_camera' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/realsense2_camera', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/realsense2_camera', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/Sensors_ROS2/realsense-ros/realsense2_camera', 'symlink_install': False, 'test_result_base': None}
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_description' build argument 'cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_description' build argument 'cmake_target' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_description' build argument 'cmake_clean_first' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_description' build argument 'cmake_force_configure' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_description' build argument 'ament_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'realsense2_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.183s] DEBUG:colcon.colcon_core.verb:Building package 'realsense2_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/realsense2_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/realsense2_description', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/Sensors_ROS2/realsense-ros/realsense2_description', 'symlink_install': False, 'test_result_base': None}
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_control' build argument 'cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_control' build argument 'cmake_target' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_control' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_control' build argument 'cmake_clean_cache' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_control' build argument 'cmake_clean_first' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_control' build argument 'cmake_force_configure' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_control' build argument 'ament_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_control' build argument 'catkin_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_control' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.183s] DEBUG:colcon.colcon_core.verb:Building package 'ymbot_d_control' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/ymbot_d_control', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/ymbot_d_control', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control', 'symlink_install': False, 'test_result_base': None}
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_moveit_config' build argument 'cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_moveit_config' build argument 'cmake_target' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_moveit_config' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_moveit_config' build argument 'cmake_clean_cache' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_moveit_config' build argument 'cmake_clean_first' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_moveit_config' build argument 'cmake_force_configure' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_moveit_config' build argument 'ament_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_moveit_config' build argument 'catkin_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_moveit_config' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.183s] DEBUG:colcon.colcon_core.verb:Building package 'ymbot_d_moveit_config' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/ymbot_d_moveit_config', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config', 'symlink_install': False, 'test_result_base': None}
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_ros2_controller' build argument 'cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_ros2_controller' build argument 'cmake_target' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_ros2_controller' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_ros2_controller' build argument 'cmake_clean_cache' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_ros2_controller' build argument 'cmake_clean_first' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_ros2_controller' build argument 'cmake_force_configure' from command line to 'False'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_ros2_controller' build argument 'ament_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_ros2_controller' build argument 'catkin_cmake_args' from command line to 'None'
[0.183s] Level 5:colcon.colcon_core.verb:set package 'ymbot_d_ros2_controller' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.183s] DEBUG:colcon.colcon_core.verb:Building package 'ymbot_d_ros2_controller' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/ws_ros2/build/ymbot_d_ros2_controller', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller', 'merge_install': False, 'path': '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller', 'symlink_install': False, 'test_result_base': None}
[0.183s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.184s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.184s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs' with build type 'ament_cmake'
[0.184s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/realsense-ros/realsense2_camera_msgs'
[0.186s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.186s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.186s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.191s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver' with build type 'ament_cmake'
[0.191s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_hardware_driver'
[0.191s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.191s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.195s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs' with build type 'ament_cmake'
[0.195s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera_msgs'
[0.195s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.195s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.200s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description' with build type 'ament_python'
[0.200s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_description', 'ament_prefix_path')
[0.200s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/hook/ament_prefix_path.ps1'
[0.201s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/hook/ament_prefix_path.dsv'
[0.201s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/hook/ament_prefix_path.sh'
[0.201s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.201s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.206s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder' with build type 'ament_python'
[0.206s] Level 1:colcon.colcon_core.shell:create_environment_hook('dataset_recorder', 'ament_prefix_path')
[0.207s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/hook/ament_prefix_path.ps1'
[0.207s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/hook/ament_prefix_path.dsv'
[0.207s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/hook/ament_prefix_path.sh'
[0.207s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.207s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.212s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/inspire_hand' with build type 'ament_python'
[0.212s] Level 1:colcon.colcon_core.shell:create_environment_hook('inspire_hand', 'ament_prefix_path')
[0.212s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/hook/ament_prefix_path.ps1'
[0.213s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/hook/ament_prefix_path.dsv'
[0.213s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/hook/ament_prefix_path.sh'
[0.214s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.214s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.219s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description' with build type 'ament_cmake'
[0.219s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_description'
[0.219s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.219s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.224s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint' with build type 'ament_cmake'
[0.225s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/udp2joint'
[0.225s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.225s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.229s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control' with build type 'ament_cmake'
[0.229s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_remote_control'
[0.229s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.229s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.234s] INFO:colcon.colcon_ros.task.ament_python.build:Building ROS package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver' with build type 'ament_python'
[0.234s] Level 1:colcon.colcon_core.shell:create_environment_hook('vr_receiver', 'ament_prefix_path')
[0.234s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/hook/ament_prefix_path.ps1'
[0.234s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/hook/ament_prefix_path.dsv'
[0.234s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/hook/ament_prefix_path.sh'
[0.235s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.235s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.243s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/realsense2_camera_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/realsense2_camera_msgs -- -j20 -l20
[0.245s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/ymbot_hardware_driver': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/ymbot_hardware_driver -- -j20 -l20
[0.246s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/orbbec_camera_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_camera_msgs -- -j20 -l20
[0.479s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description'
[0.479s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.479s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.647s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder'
[0.647s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.647s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.802s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/inspire_hand'
[0.802s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.802s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.809s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/orbbec_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_description -- -j20 -l20
[0.810s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/udp2joint': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/udp2joint -- -j20 -l20
[0.812s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery -- -j20 -l20
[0.821s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/realsense2_camera_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/realsense2_camera_msgs -- -j20 -l20
[0.830s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/realsense2_camera_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/realsense2_camera_msgs
[0.830s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/ymbot_hardware_driver' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/ymbot_hardware_driver -- -j20 -l20
[0.831s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/ymbot_hardware_driver': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/ymbot_hardware_driver
[0.832s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/orbbec_camera_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_camera_msgs -- -j20 -l20
[0.833s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/orbbec_camera_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_camera_msgs
[1.001s] INFO:colcon.colcon_core.task.python.build:Building Python package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver'
[1.001s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.001s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.025s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ymbot_hardware_driver)
[1.039s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver' for CMake module files
[1.041s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver' for CMake config files
[1.051s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/ymbot_hardware_driver' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/ymbot_hardware_driver
[1.054s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_hardware_driver', 'cmake_prefix_path')
[1.054s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/cmake_prefix_path.ps1'
[1.055s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/cmake_prefix_path.dsv'
[1.055s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/cmake_prefix_path.sh'
[1.056s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib'
[1.056s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_hardware_driver', 'ld_library_path_lib')
[1.056s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/ld_library_path_lib.ps1'
[1.056s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/ld_library_path_lib.dsv'
[1.056s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/ld_library_path_lib.sh'
[1.057s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/bin'
[1.057s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib/pkgconfig/ymbot_hardware_driver.pc'
[1.057s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib/python3.12/site-packages'
[1.057s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/bin'
[1.057s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.ps1'
[1.058s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.dsv'
[1.058s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.sh'
[1.058s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.bash'
[1.059s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.zsh'
[1.059s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/colcon-core/packages/ymbot_hardware_driver)
[1.059s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ymbot_hardware_driver)
[1.059s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver' for CMake module files
[1.060s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver' for CMake config files
[1.060s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_hardware_driver', 'cmake_prefix_path')
[1.060s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/cmake_prefix_path.ps1'
[1.060s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/cmake_prefix_path.dsv'
[1.060s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/cmake_prefix_path.sh'
[1.061s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib'
[1.061s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_hardware_driver', 'ld_library_path_lib')
[1.061s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/ld_library_path_lib.ps1'
[1.062s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/ld_library_path_lib.dsv'
[1.064s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/hook/ld_library_path_lib.sh'
[1.065s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/bin'
[1.065s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib/pkgconfig/ymbot_hardware_driver.pc'
[1.065s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib/python3.12/site-packages'
[1.065s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/bin'
[1.066s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.ps1'
[1.066s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.dsv'
[1.066s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.sh'
[1.066s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.bash'
[1.067s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/ymbot_hardware_driver/package.zsh'
[1.067s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/ymbot_hardware_driver/share/colcon-core/packages/ymbot_hardware_driver)
[1.068s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/orbbec_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_description -- -j20 -l20
[1.068s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/orbbec_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_description
[1.069s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/udp2joint' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/udp2joint -- -j20 -l20
[1.070s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/udp2joint': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/udp2joint
[1.071s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery -- -j20 -l20
[1.072s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery
[1.072s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control' with build type 'ament_cmake'
[1.072s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_control'
[1.072s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.072s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.084s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller' with build type 'ament_cmake'
[1.084s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_ros2_controller'
[1.085s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.085s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.096s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(realsense2_camera_msgs)
[1.097s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs' for CMake module files
[1.101s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/realsense2_camera_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/realsense2_camera_msgs
[1.101s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs' for CMake config files
[1.102s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera_msgs', 'cmake_prefix_path')
[1.102s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/cmake_prefix_path.ps1'
[1.102s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/cmake_prefix_path.dsv'
[1.102s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/cmake_prefix_path.sh'
[1.103s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib'
[1.103s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera_msgs', 'ld_library_path_lib')
[1.103s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/ld_library_path_lib.ps1'
[1.103s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/ld_library_path_lib.dsv'
[1.104s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/ld_library_path_lib.sh'
[1.104s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/bin'
[1.104s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/pkgconfig/realsense2_camera_msgs.pc'
[1.104s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages'
[1.104s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera_msgs', 'pythonpath')
[1.105s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/pythonpath.ps1'
[1.105s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/pythonpath.dsv'
[1.105s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/pythonpath.sh'
[1.105s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/bin'
[1.106s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.ps1'
[1.106s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.dsv'
[1.106s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.sh'
[1.106s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.bash'
[1.107s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.zsh'
[1.107s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/colcon-core/packages/realsense2_camera_msgs)
[1.107s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(realsense2_camera_msgs)
[1.107s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs' for CMake module files
[1.108s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs' for CMake config files
[1.108s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera_msgs', 'cmake_prefix_path')
[1.108s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/cmake_prefix_path.ps1'
[1.108s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/cmake_prefix_path.dsv'
[1.109s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/cmake_prefix_path.sh'
[1.109s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib'
[1.109s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera_msgs', 'ld_library_path_lib')
[1.109s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/ld_library_path_lib.ps1'
[1.109s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/ld_library_path_lib.dsv'
[1.110s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/ld_library_path_lib.sh'
[1.110s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/bin'
[1.110s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/pkgconfig/realsense2_camera_msgs.pc'
[1.110s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages'
[1.110s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera_msgs', 'pythonpath')
[1.110s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/pythonpath.ps1'
[1.110s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/pythonpath.dsv'
[1.111s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/hook/pythonpath.sh'
[1.111s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/bin'
[1.111s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.ps1'
[1.111s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.dsv'
[1.112s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.sh'
[1.112s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.bash'
[1.113s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/realsense2_camera_msgs/package.zsh'
[1.113s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/realsense2_camera_msgs/share/colcon-core/packages/realsense2_camera_msgs)
[1.113s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(orbbec_camera_msgs)
[1.113s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs' for CMake module files
[1.114s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/orbbec_camera_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_camera_msgs
[1.114s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs' for CMake config files
[1.115s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera_msgs', 'cmake_prefix_path')
[1.115s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/cmake_prefix_path.ps1'
[1.115s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/cmake_prefix_path.dsv'
[1.115s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/cmake_prefix_path.sh'
[1.116s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib'
[1.116s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera_msgs', 'ld_library_path_lib')
[1.116s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/ld_library_path_lib.ps1'
[1.116s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/ld_library_path_lib.dsv'
[1.116s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/ld_library_path_lib.sh'
[1.117s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/bin'
[1.117s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/pkgconfig/orbbec_camera_msgs.pc'
[1.117s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages'
[1.117s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera_msgs', 'pythonpath')
[1.117s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/pythonpath.ps1'
[1.117s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/pythonpath.dsv'
[1.117s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/pythonpath.sh'
[1.118s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/bin'
[1.118s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.ps1'
[1.118s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.dsv'
[1.118s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.sh'
[1.119s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.bash'
[1.119s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.zsh'
[1.119s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/colcon-core/packages/orbbec_camera_msgs)
[1.119s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(orbbec_camera_msgs)
[1.119s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs' for CMake module files
[1.120s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs' for CMake config files
[1.120s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera_msgs', 'cmake_prefix_path')
[1.120s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/cmake_prefix_path.ps1'
[1.121s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/cmake_prefix_path.dsv'
[1.121s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/cmake_prefix_path.sh'
[1.121s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib'
[1.121s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera_msgs', 'ld_library_path_lib')
[1.122s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/ld_library_path_lib.ps1'
[1.122s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/ld_library_path_lib.dsv'
[1.122s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/ld_library_path_lib.sh'
[1.122s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/bin'
[1.122s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/pkgconfig/orbbec_camera_msgs.pc'
[1.123s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages'
[1.123s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera_msgs', 'pythonpath')
[1.123s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/pythonpath.ps1'
[1.123s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/pythonpath.dsv'
[1.124s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/hook/pythonpath.sh'
[1.124s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/bin'
[1.124s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.ps1'
[1.125s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.dsv'
[1.125s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.sh'
[1.126s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.bash'
[1.126s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/orbbec_camera_msgs/package.zsh'
[1.127s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/orbbec_camera_msgs/share/colcon-core/packages/orbbec_camera_msgs)
[1.128s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera' with build type 'ament_cmake'
[1.128s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/OrbbecSDK_ROS2/orbbec_camera'
[1.128s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.128s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.134s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/realsense-ros/realsense2_camera' with build type 'ament_cmake'
[1.134s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/realsense-ros/realsense2_camera'
[1.134s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.134s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.140s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/realsense-ros/realsense2_description' with build type 'ament_cmake'
[1.141s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/Sensors_ROS2/realsense-ros/realsense2_description'
[1.141s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.141s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.147s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(orbbec_description)
[1.147s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description' for CMake module files
[1.147s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/orbbec_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_description
[1.148s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description' for CMake config files
[1.148s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_description', 'cmake_prefix_path')
[1.148s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/hook/cmake_prefix_path.ps1'
[1.148s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/hook/cmake_prefix_path.dsv'
[1.149s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/hook/cmake_prefix_path.sh'
[1.149s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description/bin'
[1.149s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description/lib/pkgconfig/orbbec_description.pc'
[1.149s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description/lib/python3.12/site-packages'
[1.149s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description/bin'
[1.150s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.ps1'
[1.150s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.dsv'
[1.150s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.sh'
[1.150s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.bash'
[1.151s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.zsh'
[1.151s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/orbbec_description/share/colcon-core/packages/orbbec_description)
[1.151s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(orbbec_description)
[1.151s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description' for CMake module files
[1.151s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description' for CMake config files
[1.152s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_description', 'cmake_prefix_path')
[1.152s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/hook/cmake_prefix_path.ps1'
[1.152s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/hook/cmake_prefix_path.dsv'
[1.152s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/hook/cmake_prefix_path.sh'
[1.153s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description/bin'
[1.153s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description/lib/pkgconfig/orbbec_description.pc'
[1.153s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description/lib/python3.12/site-packages'
[1.153s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_description/bin'
[1.153s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.ps1'
[1.153s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.dsv'
[1.154s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.sh'
[1.154s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.bash'
[1.154s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.zsh'
[1.155s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/orbbec_description/share/colcon-core/packages/orbbec_description)
[1.155s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(udp2joint)
[1.155s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint' for CMake module files
[1.156s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/udp2joint' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/udp2joint
[1.156s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint' for CMake config files
[1.156s] Level 1:colcon.colcon_core.shell:create_environment_hook('udp2joint', 'cmake_prefix_path')
[1.156s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/hook/cmake_prefix_path.ps1'
[1.157s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/hook/cmake_prefix_path.dsv'
[1.157s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/hook/cmake_prefix_path.sh'
[1.157s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/lib'
[1.157s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/bin'
[1.157s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/lib/pkgconfig/udp2joint.pc'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/lib/python3.12/site-packages'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/bin'
[1.158s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.ps1'
[1.158s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.dsv'
[1.158s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.sh'
[1.159s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.bash'
[1.159s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.zsh'
[1.159s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/udp2joint/share/colcon-core/packages/udp2joint)
[1.159s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(udp2joint)
[1.159s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint' for CMake module files
[1.160s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint' for CMake config files
[1.160s] Level 1:colcon.colcon_core.shell:create_environment_hook('udp2joint', 'cmake_prefix_path')
[1.160s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/hook/cmake_prefix_path.ps1'
[1.160s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/hook/cmake_prefix_path.dsv'
[1.160s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/hook/cmake_prefix_path.sh'
[1.161s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/lib'
[1.161s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/bin'
[1.161s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/lib/pkgconfig/udp2joint.pc'
[1.161s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/lib/python3.12/site-packages'
[1.161s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/udp2joint/bin'
[1.161s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.ps1'
[1.162s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.dsv'
[1.162s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.sh'
[1.162s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.bash'
[1.162s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.zsh'
[1.163s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/udp2joint/share/colcon-core/packages/udp2joint)
[1.163s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(vr_pose_tracking_singularity_recovery)
[1.163s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery' for CMake module files
[1.164s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery' for CMake config files
[1.165s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/vr_pose_tracking_singularity_recovery
[1.165s] Level 1:colcon.colcon_core.shell:create_environment_hook('vr_pose_tracking_singularity_recovery', 'cmake_prefix_path')
[1.165s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/hook/cmake_prefix_path.ps1'
[1.165s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/hook/cmake_prefix_path.dsv'
[1.166s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/hook/cmake_prefix_path.sh'
[1.166s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/lib'
[1.166s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/bin'
[1.166s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/lib/pkgconfig/vr_pose_tracking_singularity_recovery.pc'
[1.167s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/lib/python3.12/site-packages'
[1.167s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/bin'
[1.167s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.ps1'
[1.167s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.dsv'
[1.167s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.sh'
[1.168s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.bash'
[1.168s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.zsh'
[1.168s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/colcon-core/packages/vr_pose_tracking_singularity_recovery)
[1.168s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(vr_pose_tracking_singularity_recovery)
[1.169s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery' for CMake module files
[1.169s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery' for CMake config files
[1.169s] Level 1:colcon.colcon_core.shell:create_environment_hook('vr_pose_tracking_singularity_recovery', 'cmake_prefix_path')
[1.169s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/hook/cmake_prefix_path.ps1'
[1.170s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/hook/cmake_prefix_path.dsv'
[1.170s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/hook/cmake_prefix_path.sh'
[1.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/lib'
[1.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/bin'
[1.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/lib/pkgconfig/vr_pose_tracking_singularity_recovery.pc'
[1.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/lib/python3.12/site-packages'
[1.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/bin'
[1.171s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.ps1'
[1.172s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.dsv'
[1.172s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.sh'
[1.172s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.bash'
[1.172s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/vr_pose_tracking_singularity_recovery/package.zsh'
[1.173s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery/share/colcon-core/packages/vr_pose_tracking_singularity_recovery)
[1.176s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/ymbot_d_control': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib /usr/bin/cmake --build /home/<USER>/ws_ros2/build/ymbot_d_control -- -j20 -l20
[1.179s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/ymbot_d_ros2_controller': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib /usr/bin/cmake --build /home/<USER>/ws_ros2/build/ymbot_d_ros2_controller -- -j20 -l20
[1.181s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/orbbec_camera': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_camera -- -j20 -l20
[1.183s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/realsense2_camera': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/ws_ros2/build/realsense2_camera -- -j20 -l20
[1.184s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/realsense2_description': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/ws_ros2/build/realsense2_description -- -j20 -l20
[1.238s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/realsense2_description' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/ws_ros2/build/realsense2_description -- -j20 -l20
[1.238s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/realsense2_description': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/ws_ros2/build/realsense2_description
[1.270s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(realsense2_description)
[1.270s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description' for CMake module files
[1.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description' for CMake config files
[1.271s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_description', 'cmake_prefix_path')
[1.271s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/hook/cmake_prefix_path.ps1'
[1.272s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/realsense2_description' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/ws_ros2/build/realsense2_description
[1.273s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/hook/cmake_prefix_path.dsv'
[1.273s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/hook/cmake_prefix_path.sh'
[1.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description/bin'
[1.274s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description/lib/pkgconfig/realsense2_description.pc'
[1.275s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description/lib/python3.12/site-packages'
[1.275s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description/bin'
[1.276s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.ps1'
[1.276s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.dsv'
[1.277s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.sh'
[1.280s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.bash'
[1.280s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.zsh'
[1.281s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/realsense2_description/share/colcon-core/packages/realsense2_description)
[1.282s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(realsense2_description)
[1.282s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description' for CMake module files
[1.283s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description' for CMake config files
[1.283s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_description', 'cmake_prefix_path')
[1.284s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/hook/cmake_prefix_path.ps1'
[1.284s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/hook/cmake_prefix_path.dsv'
[1.285s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/hook/cmake_prefix_path.sh'
[1.286s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description/bin'
[1.286s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description/lib/pkgconfig/realsense2_description.pc'
[1.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description/lib/python3.12/site-packages'
[1.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_description/bin'
[1.287s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.ps1'
[1.288s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.dsv'
[1.289s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.sh'
[1.290s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.bash'
[1.291s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_description/share/realsense2_description/package.zsh'
[1.291s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/realsense2_description/share/colcon-core/packages/realsense2_description)
[1.299s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/ymbot_d_ros2_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib /usr/bin/cmake --build /home/<USER>/ws_ros2/build/ymbot_d_ros2_controller -- -j20 -l20
[1.301s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/ymbot_d_ros2_controller': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib /usr/bin/cmake --install /home/<USER>/ws_ros2/build/ymbot_d_ros2_controller
[1.314s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ymbot_d_ros2_controller)
[1.315s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller' for CMake module files
[1.315s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/ymbot_d_ros2_controller' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib /usr/bin/cmake --install /home/<USER>/ws_ros2/build/ymbot_d_ros2_controller
[1.316s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller' for CMake config files
[1.316s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_ros2_controller', 'cmake_prefix_path')
[1.316s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/cmake_prefix_path.ps1'
[1.317s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/cmake_prefix_path.dsv'
[1.317s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/cmake_prefix_path.sh'
[1.318s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib'
[1.318s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_ros2_controller', 'ld_library_path_lib')
[1.318s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/ld_library_path_lib.ps1'
[1.318s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/ld_library_path_lib.dsv'
[1.318s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/ld_library_path_lib.sh'
[1.319s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/bin'
[1.319s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib/pkgconfig/ymbot_d_ros2_controller.pc'
[1.319s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib/python3.12/site-packages'
[1.319s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/bin'
[1.319s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.ps1'
[1.320s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.dsv'
[1.320s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.sh'
[1.320s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.bash'
[1.320s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.zsh'
[1.321s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/colcon-core/packages/ymbot_d_ros2_controller)
[1.321s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ymbot_d_ros2_controller)
[1.321s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller' for CMake module files
[1.322s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller' for CMake config files
[1.322s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_ros2_controller', 'cmake_prefix_path')
[1.322s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/cmake_prefix_path.ps1'
[1.322s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/cmake_prefix_path.dsv'
[1.323s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/cmake_prefix_path.sh'
[1.323s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib'
[1.323s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_ros2_controller', 'ld_library_path_lib')
[1.323s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/ld_library_path_lib.ps1'
[1.324s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/ld_library_path_lib.dsv'
[1.324s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/hook/ld_library_path_lib.sh'
[1.324s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/bin'
[1.324s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib/pkgconfig/ymbot_d_ros2_controller.pc'
[1.324s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib/python3.12/site-packages'
[1.325s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/bin'
[1.325s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.ps1'
[1.325s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.dsv'
[1.326s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.sh'
[1.326s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.bash'
[1.327s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/ymbot_d_ros2_controller/package.zsh'
[1.327s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/share/colcon-core/packages/ymbot_d_ros2_controller)
[1.329s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/realsense2_camera' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/ws_ros2/build/realsense2_camera -- -j20 -l20
[1.331s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/realsense2_camera': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/ws_ros2/build/realsense2_camera
[1.332s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/ymbot_d_control' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib /usr/bin/cmake --build /home/<USER>/ws_ros2/build/ymbot_d_control -- -j20 -l20
[1.333s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/ymbot_d_control': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib /usr/bin/cmake --install /home/<USER>/ws_ros2/build/ymbot_d_control
[1.348s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ymbot_d_control)
[1.348s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control' for CMake module files
[1.353s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control' for CMake config files
[1.354s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/ymbot_d_control' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib /usr/bin/cmake --install /home/<USER>/ws_ros2/build/ymbot_d_control
[1.354s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_control', 'cmake_prefix_path')
[1.354s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/hook/cmake_prefix_path.ps1'
[1.355s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/hook/cmake_prefix_path.dsv'
[1.355s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/hook/cmake_prefix_path.sh'
[1.356s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/lib'
[1.356s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/bin'
[1.356s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/lib/pkgconfig/ymbot_d_control.pc'
[1.357s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/lib/python3.12/site-packages'
[1.357s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/bin'
[1.357s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.ps1'
[1.358s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.dsv'
[1.358s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.sh'
[1.359s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.bash'
[1.359s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.zsh'
[1.360s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/ymbot_d_control/share/colcon-core/packages/ymbot_d_control)
[1.360s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ymbot_d_control)
[1.360s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control' for CMake module files
[1.361s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control' for CMake config files
[1.362s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_control', 'cmake_prefix_path')
[1.362s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/hook/cmake_prefix_path.ps1'
[1.362s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/hook/cmake_prefix_path.dsv'
[1.363s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/hook/cmake_prefix_path.sh'
[1.363s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/lib'
[1.364s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/bin'
[1.364s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/lib/pkgconfig/ymbot_d_control.pc'
[1.364s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/lib/python3.12/site-packages'
[1.364s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_control/bin'
[1.364s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.ps1'
[1.365s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.dsv'
[1.366s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.sh'
[1.366s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.bash'
[1.367s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_control/share/ymbot_d_control/package.zsh'
[1.367s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/ymbot_d_control/share/colcon-core/packages/ymbot_d_control)
[1.367s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(realsense2_camera)
[1.368s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera' for CMake module files
[1.368s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera' for CMake config files
[1.368s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/realsense2_camera' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/ws_ros2/build/realsense2_camera
[1.369s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera', 'cmake_prefix_path')
[1.369s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/cmake_prefix_path.ps1'
[1.369s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/cmake_prefix_path.dsv'
[1.369s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/cmake_prefix_path.sh'
[1.370s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/lib'
[1.370s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera', 'ld_library_path_lib')
[1.370s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/ld_library_path_lib.ps1'
[1.370s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/ld_library_path_lib.dsv'
[1.370s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/ld_library_path_lib.sh'
[1.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/bin'
[1.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/lib/pkgconfig/realsense2_camera.pc'
[1.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/lib/python3.12/site-packages'
[1.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/bin'
[1.371s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.ps1'
[1.371s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.dsv'
[1.372s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.sh'
[1.372s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.bash'
[1.372s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.zsh'
[1.372s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/realsense2_camera/share/colcon-core/packages/realsense2_camera)
[1.373s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(realsense2_camera)
[1.373s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera' for CMake module files
[1.373s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera' for CMake config files
[1.373s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera', 'cmake_prefix_path')
[1.374s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/cmake_prefix_path.ps1'
[1.374s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/cmake_prefix_path.dsv'
[1.374s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/cmake_prefix_path.sh'
[1.374s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/lib'
[1.374s] Level 1:colcon.colcon_core.shell:create_environment_hook('realsense2_camera', 'ld_library_path_lib')
[1.375s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/ld_library_path_lib.ps1'
[1.375s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/ld_library_path_lib.dsv'
[1.375s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/hook/ld_library_path_lib.sh'
[1.375s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/bin'
[1.375s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/lib/pkgconfig/realsense2_camera.pc'
[1.375s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/lib/python3.12/site-packages'
[1.376s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/realsense2_camera/bin'
[1.376s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.ps1'
[1.376s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.dsv'
[1.376s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.sh'
[1.377s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.bash'
[1.377s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/realsense2_camera/share/realsense2_camera/package.zsh'
[1.377s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/realsense2_camera/share/colcon-core/packages/realsense2_camera)
[1.379s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/orbbec_camera' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_camera -- -j20 -l20
[1.380s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/orbbec_camera': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_camera
[1.400s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(orbbec_camera)
[1.401s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera' for CMake module files
[1.407s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera' for CMake config files
[1.410s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/orbbec_camera' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_camera
[1.410s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera', 'cmake_prefix_path')
[1.410s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/cmake_prefix_path.ps1'
[1.411s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/cmake_prefix_path.dsv'
[1.411s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/cmake_prefix_path.sh'
[1.412s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/lib'
[1.412s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera', 'ld_library_path_lib')
[1.412s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/ld_library_path_lib.ps1'
[1.413s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/ld_library_path_lib.dsv'
[1.413s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/ld_library_path_lib.sh'
[1.414s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/bin'
[1.414s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/lib/pkgconfig/orbbec_camera.pc'
[1.414s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/lib/python3.12/site-packages'
[1.414s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/bin'
[1.415s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.ps1'
[1.415s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.dsv'
[1.415s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.sh'
[1.416s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.bash'
[1.416s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.zsh'
[1.416s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/orbbec_camera/share/colcon-core/packages/orbbec_camera)
[1.416s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(orbbec_camera)
[1.416s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera' for CMake module files
[1.417s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera' for CMake config files
[1.417s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera', 'cmake_prefix_path')
[1.417s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/cmake_prefix_path.ps1'
[1.417s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/cmake_prefix_path.dsv'
[1.418s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/cmake_prefix_path.sh'
[1.418s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/lib'
[1.418s] Level 1:colcon.colcon_core.shell:create_environment_hook('orbbec_camera', 'ld_library_path_lib')
[1.418s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/ld_library_path_lib.ps1'
[1.418s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/ld_library_path_lib.dsv'
[1.418s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/hook/ld_library_path_lib.sh'
[1.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/bin'
[1.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/lib/pkgconfig/orbbec_camera.pc'
[1.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/lib/python3.12/site-packages'
[1.419s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/orbbec_camera/bin'
[1.419s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.ps1'
[1.420s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.dsv'
[1.420s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.sh'
[1.420s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.bash'
[1.421s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.zsh'
[1.421s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/orbbec_camera/share/colcon-core/packages/orbbec_camera)
[1.563s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/dataset_recorder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/dataset_recorder build --build-base /home/<USER>/ws_ros2/build/dataset_recorder/build install --record /home/<USER>/ws_ros2/build/dataset_recorder/install.log --single-version-externally-managed install_data
[1.617s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/ymbot_d_description/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/ymbot_d_description build --build-base /home/<USER>/ws_ros2/build/ymbot_d_description/build install --record /home/<USER>/ws_ros2/build/ymbot_d_description/install.log --single-version-externally-managed install_data
[1.637s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/src/Sensors_ROS2/inspire_hand': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/inspire_hand/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/inspire_hand build --build-base /home/<USER>/ws_ros2/build/inspire_hand/build install --record /home/<USER>/ws_ros2/build/inspire_hand/install.log --single-version-externally-managed install_data
[1.668s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/vr_receiver/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_receiver build --build-base /home/<USER>/ws_ros2/build/vr_receiver/build install --record /home/<USER>/ws_ros2/build/vr_receiver/install.log --single-version-externally-managed install_data
[2.000s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/dataset_recorder' for CMake module files
[2.001s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/dataset_recorder' for CMake config files
[2.001s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/dataset_recorder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/dataset_recorder build --build-base /home/<USER>/ws_ros2/build/dataset_recorder/build install --record /home/<USER>/ws_ros2/build/dataset_recorder/install.log --single-version-externally-managed install_data
[2.002s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/dataset_recorder/lib'
[2.002s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/dataset_recorder/bin'
[2.002s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/dataset_recorder/lib/pkgconfig/dataset_recorder.pc'
[2.002s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages'
[2.002s] Level 1:colcon.colcon_core.shell:create_environment_hook('dataset_recorder', 'pythonpath')
[2.002s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/hook/pythonpath.ps1'
[2.003s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/hook/pythonpath.dsv'
[2.003s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/hook/pythonpath.sh'
[2.003s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/dataset_recorder/bin'
[2.003s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(dataset_recorder)
[2.003s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/package.ps1'
[2.004s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/package.dsv'
[2.004s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/package.sh'
[2.004s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/package.bash'
[2.004s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/dataset_recorder/share/dataset_recorder/package.zsh'
[2.004s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/dataset_recorder/share/colcon-core/packages/dataset_recorder)
[2.036s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_description' for CMake module files
[2.037s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_description' for CMake config files
[2.037s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_description' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/ymbot_d_description/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/ymbot_d_description build --build-base /home/<USER>/ws_ros2/build/ymbot_d_description/build install --record /home/<USER>/ws_ros2/build/ymbot_d_description/install.log --single-version-externally-managed install_data
[2.038s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_description/lib'
[2.038s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_description/bin'
[2.038s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_description/lib/pkgconfig/ymbot_d_description.pc'
[2.038s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages'
[2.038s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_description', 'pythonpath')
[2.038s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/hook/pythonpath.ps1'
[2.038s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/hook/pythonpath.dsv'
[2.039s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/hook/pythonpath.sh'
[2.039s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_description/bin'
[2.039s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ymbot_d_description)
[2.039s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/package.ps1'
[2.040s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/package.dsv'
[2.040s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/package.sh'
[2.040s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/package.bash'
[2.040s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_description/share/ymbot_d_description/package.zsh'
[2.041s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/ymbot_d_description/share/colcon-core/packages/ymbot_d_description)
[2.041s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config' with build type 'ament_cmake'
[2.041s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/ymbot_d_moveit_config'
[2.041s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[2.041s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[2.060s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/ymbot_d_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_configs_utils:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/ymbot_d_moveit_config -- -j20 -l20
[2.088s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/inspire_hand' for CMake module files
[2.089s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/src/Sensors_ROS2/inspire_hand' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/inspire_hand/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/inspire_hand build --build-base /home/<USER>/ws_ros2/build/inspire_hand/build install --record /home/<USER>/ws_ros2/build/inspire_hand/install.log --single-version-externally-managed install_data
[2.089s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/inspire_hand' for CMake config files
[2.090s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/inspire_hand/lib'
[2.090s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/inspire_hand/bin'
[2.090s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/inspire_hand/lib/pkgconfig/inspire_hand.pc'
[2.090s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages'
[2.090s] Level 1:colcon.colcon_core.shell:create_environment_hook('inspire_hand', 'pythonpath')
[2.090s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/hook/pythonpath.ps1'
[2.091s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/hook/pythonpath.dsv'
[2.091s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/hook/pythonpath.sh'
[2.091s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/inspire_hand/bin'
[2.091s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(inspire_hand)
[2.092s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/package.ps1'
[2.092s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/package.dsv'
[2.092s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/package.sh'
[2.092s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/package.bash'
[2.092s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/inspire_hand/share/inspire_hand/package.zsh'
[2.093s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/inspire_hand/share/colcon-core/packages/inspire_hand)
[2.107s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_receiver' for CMake module files
[2.108s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/vr_receiver' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/vr_receiver/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/vr_receiver build --build-base /home/<USER>/ws_ros2/build/vr_receiver/build install --record /home/<USER>/ws_ros2/build/vr_receiver/install.log --single-version-externally-managed install_data
[2.108s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_receiver' for CMake config files
[2.109s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_receiver/lib'
[2.109s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_receiver/bin'
[2.109s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_receiver/lib/pkgconfig/vr_receiver.pc'
[2.109s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages'
[2.109s] Level 1:colcon.colcon_core.shell:create_environment_hook('vr_receiver', 'pythonpath')
[2.109s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/hook/pythonpath.ps1'
[2.110s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/hook/pythonpath.dsv'
[2.110s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/hook/pythonpath.sh'
[2.110s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/vr_receiver/bin'
[2.110s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(vr_receiver)
[2.111s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/package.ps1'
[2.111s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/package.dsv'
[2.111s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/package.sh'
[2.111s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/package.bash'
[2.111s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/vr_receiver/share/vr_receiver/package.zsh'
[2.112s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/vr_receiver/share/colcon-core/packages/vr_receiver)
[2.113s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/ymbot_d_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_configs_utils:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/ymbot_d_moveit_config -- -j20 -l20
[2.114s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/ws_ros2/build/ymbot_d_moveit_config': AMENT_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_configs_utils:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/ymbot_d_moveit_config
[2.125s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ymbot_d_moveit_config)
[2.126s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config' for CMake module files
[2.126s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/ws_ros2/build/ymbot_d_moveit_config' returned '0': AMENT_PREFIX_PATH=/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_configs_utils:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/ymbot_d_moveit_config
[2.127s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config' for CMake config files
[2.127s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_moveit_config', 'cmake_prefix_path')
[2.127s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/hook/cmake_prefix_path.ps1'
[2.128s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/hook/cmake_prefix_path.dsv'
[2.128s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/hook/cmake_prefix_path.sh'
[2.129s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/bin'
[2.129s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/lib/pkgconfig/ymbot_d_moveit_config.pc'
[2.129s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/lib/python3.12/site-packages'
[2.129s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/bin'
[2.129s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.ps1'
[2.130s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.dsv'
[2.130s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.sh'
[2.130s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.bash'
[2.130s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.zsh'
[2.131s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/colcon-core/packages/ymbot_d_moveit_config)
[2.131s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(ymbot_d_moveit_config)
[2.131s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config' for CMake module files
[2.131s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config' for CMake config files
[2.131s] Level 1:colcon.colcon_core.shell:create_environment_hook('ymbot_d_moveit_config', 'cmake_prefix_path')
[2.131s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/hook/cmake_prefix_path.ps1'
[2.132s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/hook/cmake_prefix_path.dsv'
[2.132s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/hook/cmake_prefix_path.sh'
[2.132s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/bin'
[2.132s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/lib/pkgconfig/ymbot_d_moveit_config.pc'
[2.132s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/lib/python3.12/site-packages'
[2.132s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/bin'
[2.132s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.ps1'
[2.133s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.dsv'
[2.133s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.sh'
[2.133s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.bash'
[2.133s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/ymbot_d_moveit_config/package.zsh'
[2.134s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/ws_ros2/install/ymbot_d_moveit_config/share/colcon-core/packages/ymbot_d_moveit_config)
[2.134s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[2.134s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[2.134s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[2.134s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[2.139s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[2.139s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[2.139s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[2.150s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[2.150s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_ros2/install/local_setup.ps1'
[2.151s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ws_ros2/install/_local_setup_util_ps1.py'
[2.152s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_ros2/install/setup.ps1'
[2.153s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_ros2/install/local_setup.sh'
[2.154s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/ws_ros2/install/_local_setup_util_sh.py'
[2.154s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_ros2/install/setup.sh'
[2.156s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_ros2/install/local_setup.bash'
[2.156s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_ros2/install/setup.bash'
[2.158s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/ws_ros2/install/local_setup.zsh'
[2.158s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/ws_ros2/install/setup.zsh'
