Invoking command in '/home/<USER>/ws_ros2/src/Sensors_ROS2/inspire_hand': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/inspire_hand/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/inspire_hand build --build-base /home/<USER>/ws_ros2/build/inspire_hand/build install --record /home/<USER>/ws_ros2/build/inspire_hand/install.log --single-version-externally-managed install_data
Invoked command in '/home/<USER>/ws_ros2/src/Sensors_ROS2/inspire_hand' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/inspire_hand/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/inspire_hand build --build-base /home/<USER>/ws_ros2/build/inspire_hand/build install --record /home/<USER>/ws_ros2/build/inspire_hand/install.log --single-version-externally-managed install_data
