running egg_info
writing ../../../build/inspire_hand/inspire_hand.egg-info/PKG-INFO
writing dependency_links to ../../../build/inspire_hand/inspire_hand.egg-info/dependency_links.txt
writing entry points to ../../../build/inspire_hand/inspire_hand.egg-info/entry_points.txt
writing requirements to ../../../build/inspire_hand/inspire_hand.egg-info/requires.txt
writing top-level names to ../../../build/inspire_hand/inspire_hand.egg-info/top_level.txt
reading manifest file '../../../build/inspire_hand/inspire_hand.egg-info/SOURCES.txt'
writing manifest file '../../../build/inspire_hand/inspire_hand.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages/inspire_hand-0.0.1-py3.12.egg-info' (and everything under it)
Copying ../../../build/inspire_hand/inspire_hand.egg-info to /home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages/inspire_hand-0.0.1-py3.12.egg-info
running install_scripts
Installing demo_inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
Installing inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
Installing single_hand_command script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
Installing zn_carry_inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
Installing zn_pick_inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
Installing zn_scan_inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
writing list of installed files to '/home/<USER>/ws_ros2/build/inspire_hand/install.log'
