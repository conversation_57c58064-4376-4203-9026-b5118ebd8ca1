[0.585s] Invoking command in '/home/<USER>/ws_ros2/build/udp2joint': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/udp2joint -- -j20 -l20
[0.648s] [ 50%] Built target dlltest
[0.651s] [100%] Built target moveit_test
[0.844s] Invoked command in '/home/<USER>/ws_ros2/build/udp2joint' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/udp2joint -- -j20 -l20
[0.845s] Invoking command in '/home/<USER>/ws_ros2/build/udp2joint': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/udp2joint
[0.874s] -- Install configuration: ""
[0.874s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/lib/udp2joint/dlltest
[0.874s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/lib/udp2joint/moveit_test
[0.874s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/launch
[0.874s] -- Installing: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/launch/run.launch.py
[0.874s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/launch/udptest.launch.py
[0.874s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/launch/lbs_run.launch.py
[0.874s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//config
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//config/controllers.yaml
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//rviz
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//rviz/rviz2.rviz
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint//rviz/rviz.rviz
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/ament_index/resource_index/package_run_dependencies/udp2joint
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/ament_index/resource_index/parent_prefix_path/udp2joint
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/environment/ament_prefix_path.sh
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/environment/ament_prefix_path.dsv
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/environment/path.sh
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/environment/path.dsv
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/local_setup.bash
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/local_setup.sh
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/local_setup.zsh
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/local_setup.dsv
[0.875s] -- Installing: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.dsv
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/ament_index/resource_index/packages/udp2joint
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/cmake/udp2jointConfig.cmake
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/cmake/udp2jointConfig-version.cmake
[0.875s] -- Up-to-date: /home/<USER>/ws_ros2/install/udp2joint/share/udp2joint/package.xml
[0.931s] Invoked command in '/home/<USER>/ws_ros2/build/udp2joint' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/udp2joint
