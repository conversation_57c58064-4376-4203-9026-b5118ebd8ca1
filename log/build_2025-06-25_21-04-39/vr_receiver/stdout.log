running egg_info
writing ../../../build/vr_receiver/vr_receiver.egg-info/PKG-INFO
writing dependency_links to ../../../build/vr_receiver/vr_receiver.egg-info/dependency_links.txt
writing entry points to ../../../build/vr_receiver/vr_receiver.egg-info/entry_points.txt
writing requirements to ../../../build/vr_receiver/vr_receiver.egg-info/requires.txt
writing top-level names to ../../../build/vr_receiver/vr_receiver.egg-info/top_level.txt
reading manifest file '../../../build/vr_receiver/vr_receiver.egg-info/SOURCES.txt'
writing manifest file '../../../build/vr_receiver/vr_receiver.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages/vr_receiver-0.0.0-py3.12.egg-info' (and everything under it)
Copying ../../../build/vr_receiver/vr_receiver.egg-info to /home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages/vr_receiver-0.0.0-py3.12.egg-info
running install_scripts
Installing demo_vr_receiver script to /home/<USER>/ws_ros2/install/vr_receiver/lib/vr_receiver
Installing vr_receiver script to /home/<USER>/ws_ros2/install/vr_receiver/lib/vr_receiver
writing list of installed files to '/home/<USER>/ws_ros2/build/vr_receiver/install.log'
