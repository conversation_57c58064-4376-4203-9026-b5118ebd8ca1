running egg_info
writing ../../../build/dataset_recorder/dataset_recorder.egg-info/PKG-INFO
writing dependency_links to ../../../build/dataset_recorder/dataset_recorder.egg-info/dependency_links.txt
writing entry points to ../../../build/dataset_recorder/dataset_recorder.egg-info/entry_points.txt
writing requirements to ../../../build/dataset_recorder/dataset_recorder.egg-info/requires.txt
writing top-level names to ../../../build/dataset_recorder/dataset_recorder.egg-info/top_level.txt
reading manifest file '../../../build/dataset_recorder/dataset_recorder.egg-info/SOURCES.txt'
writing manifest file '../../../build/dataset_recorder/dataset_recorder.egg-info/SOURCES.txt'
running build
running build_py
running install
running install_lib
running install_data
running install_egg_info
removing '/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages/dataset_recorder-0.0.0-py3.12.egg-info' (and everything under it)
Copying ../../../build/dataset_recorder/dataset_recorder.egg-info to /home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages/dataset_recorder-0.0.0-py3.12.egg-info
running install_scripts
Installing dataset_recorder_node script to /home/<USER>/ws_ros2/install/dataset_recorder/lib/dataset_recorder
writing list of installed files to '/home/<USER>/ws_ros2/build/dataset_recorder/install.log'
