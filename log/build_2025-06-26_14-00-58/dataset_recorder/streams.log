[1.567s] Invoking command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/dataset_recorder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/dataset_recorder build --build-base /home/<USER>/ws_ros2/build/dataset_recorder/build install --record /home/<USER>/ws_ros2/build/dataset_recorder/install.log --single-version-externally-managed install_data
[1.811s] running egg_info
[1.829s] writing ../../../build/dataset_recorder/dataset_recorder.egg-info/PKG-INFO
[1.829s] writing dependency_links to ../../../build/dataset_recorder/dataset_recorder.egg-info/dependency_links.txt
[1.830s] writing entry points to ../../../build/dataset_recorder/dataset_recorder.egg-info/entry_points.txt
[1.830s] writing requirements to ../../../build/dataset_recorder/dataset_recorder.egg-info/requires.txt
[1.830s] writing top-level names to ../../../build/dataset_recorder/dataset_recorder.egg-info/top_level.txt
[1.866s] reading manifest file '../../../build/dataset_recorder/dataset_recorder.egg-info/SOURCES.txt'
[1.867s] writing manifest file '../../../build/dataset_recorder/dataset_recorder.egg-info/SOURCES.txt'
[1.867s] running build
[1.867s] running build_py
[1.868s] running install
[1.872s] running install_lib
[1.891s] running install_data
[1.891s] running install_egg_info
[1.910s] removing '/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages/dataset_recorder-0.0.0-py3.12.egg-info' (and everything under it)
[1.910s] Copying ../../../build/dataset_recorder/dataset_recorder.egg-info to /home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages/dataset_recorder-0.0.0-py3.12.egg-info
[1.911s] running install_scripts
[2.027s] Installing dataset_recorder_node script to /home/<USER>/ws_ros2/install/dataset_recorder/lib/dataset_recorder
[2.028s] writing list of installed files to '/home/<USER>/ws_ros2/build/dataset_recorder/install.log'
[2.061s] Invoked command in '/home/<USER>/ws_ros2/src/ymbot_d-Ros2_Jazzy_RemoteOperation_RelativeControl/dataset_recorder' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/dataset_recorder/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/dataset_recorder build --build-base /home/<USER>/ws_ros2/build/dataset_recorder/build install --record /home/<USER>/ws_ros2/build/dataset_recorder/install.log --single-version-externally-managed install_data
