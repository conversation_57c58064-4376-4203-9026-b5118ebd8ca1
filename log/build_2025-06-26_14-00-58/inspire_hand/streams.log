[1.549s] Invoking command in '/home/<USER>/ws_ros2/src/Sensors_ROS2/inspire_hand': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/inspire_hand/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/inspire_hand build --build-base /home/<USER>/ws_ros2/build/inspire_hand/build install --record /home/<USER>/ws_ros2/build/inspire_hand/install.log --single-version-externally-managed install_data
[1.790s] running egg_info
[1.808s] writing ../../../build/inspire_hand/inspire_hand.egg-info/PKG-INFO
[1.808s] writing dependency_links to ../../../build/inspire_hand/inspire_hand.egg-info/dependency_links.txt
[1.808s] writing entry points to ../../../build/inspire_hand/inspire_hand.egg-info/entry_points.txt
[1.809s] writing requirements to ../../../build/inspire_hand/inspire_hand.egg-info/requires.txt
[1.809s] writing top-level names to ../../../build/inspire_hand/inspire_hand.egg-info/top_level.txt
[1.853s] reading manifest file '../../../build/inspire_hand/inspire_hand.egg-info/SOURCES.txt'
[1.854s] writing manifest file '../../../build/inspire_hand/inspire_hand.egg-info/SOURCES.txt'
[1.854s] running build
[1.854s] running build_py
[1.854s] running install
[1.859s] running install_lib
[1.880s] running install_data
[1.880s] running install_egg_info
[1.899s] removing '/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages/inspire_hand-0.0.1-py3.12.egg-info' (and everything under it)
[1.899s] Copying ../../../build/inspire_hand/inspire_hand.egg-info to /home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages/inspire_hand-0.0.1-py3.12.egg-info
[1.900s] running install_scripts
[2.026s] Installing demo_inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
[2.026s] Installing inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
[2.027s] Installing single_hand_command script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
[2.027s] Installing zn_carry_inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
[2.027s] Installing zn_pick_inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
[2.027s] Installing zn_scan_inspire_hand script to /home/<USER>/ws_ros2/install/inspire_hand/lib/inspire_hand
[2.027s] writing list of installed files to '/home/<USER>/ws_ros2/build/inspire_hand/install.log'
[2.063s] Invoked command in '/home/<USER>/ws_ros2/src/Sensors_ROS2/inspire_hand' returned '0': DEBUGINFOD_URLS=https://debuginfod.ubuntu.com PYTHONPATH=/home/<USER>/ws_ros2/build/inspire_hand/prefix_override:/usr/lib/python3/dist-packages/colcon_core/task/python/colcon_distutils_commands:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:${PYTHONPATH} /usr/bin/python3 -W ignore:setup.py install is deprecated -W ignore:easy_install command is deprecated setup.py egg_info --egg-base ../../../build/inspire_hand build --build-base /home/<USER>/ws_ros2/build/inspire_hand/build install --record /home/<USER>/ws_ros2/build/inspire_hand/install.log --single-version-externally-managed install_data
