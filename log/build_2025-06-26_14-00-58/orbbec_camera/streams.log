[0.073s] Invoking command in '/home/<USER>/ws_ros2/build/orbbec_camera': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_camera -- -j20 -l20
[0.165s] [  8%] Built target frame_latency_node
[0.168s] [ 16%] Built target orbbec_camera_node
[0.170s] [ 24%] Built target frame_latency
[0.204s] [ 68%] Built target orbbec_camera
[0.246s] [ 76%] Built target list_depth_work_mode_node
[0.247s] [ 92%] Built target list_camera_profile_mode_node
[0.247s] [ 92%] Built target topic_statistics_node
[0.251s] [100%] Built target list_devices_node
[0.274s] Invoked command in '/home/<USER>/ws_ros2/build/orbbec_camera' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_camera -- -j20 -l20
[0.275s] Invoking command in '/home/<USER>/ws_ros2/build/orbbec_camera': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_camera
[0.289s] -- Install configuration: "Release"
[0.290s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib/orbbec_camera/orbbec_camera_node
[0.291s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib/orbbec_camera/frame_latency_node
[0.292s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib/liborbbec_camera.so
[0.293s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib/libframe_latency.so
[0.293s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include
[0.293s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera
[0.293s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/synced_imu_publisher.h
[0.293s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/jpeg_decoder.h
[0.293s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/image_publisher.h
[0.293s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/dynamic_params.h
[0.294s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/d2c_viewer.h
[0.294s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/ros_param_backend.h
[0.294s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/utils.h
[0.294s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/jetson_nv_decoder.h
[0.294s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/ob_camera_node.h
[0.294s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/rk_mpp_decoder.h
[0.295s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/ob_camera_node_driver.h
[0.295s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/orbbec_camera/constants.h
[0.295s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/magic_enum
[0.295s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/magic_enum/magic_enum.hpp
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/astra_adv.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai_dcw2.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/femto_net_camera.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/multi_camera.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/astra_stereo_u3.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/astra_pro2.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/astra.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini2XL.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/femto.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai_max_pro.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai_pro.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai_d1.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/femto_mega.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini2VL.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai_dw2.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/multi_net_camera.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai_dcl.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini_uw.launch.py
[0.296s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini2L.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/multi_camera_synced.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini2.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini_e_lite.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/deeya.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai_dw.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini_330_series.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/astra_embedded_s.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/femto_bolt.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini_e.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai_dcw.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini_intra_process_demo_launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/astra2.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai_max.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/ob_camera.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/dabai.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini_ew_lite.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//launch/gemini_ew.launch.py
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//config
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//config/.gitkeep
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//config/OrbbecSDKConfig_v1.0.xml
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//config/depthfilter
[0.297s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//config/depthfilter/Openni_device.json
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//config/depthfilter/Gemini2_v1.8.json
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera//config/camera_params.yaml
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/ObTypes.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Filter.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Pipeline.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Frame.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Context.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/RecordPlayback.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/StreamProfile.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Sensor.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Property.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Error.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Device.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Utils.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/MultipleDevices.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/h/Version.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/ObSensor.h
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/ObSensor.hpp
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Types.hpp
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/StreamProfile.hpp
[0.298s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Filter.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Pipeline.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Version.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Error.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Sensor.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Utils.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Frame.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/RecordPlayback.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Context.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/include/libobsensor/hpp/Device.hpp
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib/
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib//libob_usb.so
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib//libdepthengine.so.2.0
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib//libdepthengine.so
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib//libOrbbecSDK.so
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib//liblive555.so
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib//libOrbbecSDK.so.1.10.18
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib//libOrbbecSDK.so.1.10
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib/orbbec_camera/list_devices_node
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib/orbbec_camera/list_depth_work_mode_node
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib/orbbec_camera/list_camera_profile_mode_node
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/lib/orbbec_camera/topic_statistics_node
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/environment/library_path.sh
[0.299s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/environment/library_path.dsv
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/ament_index/resource_index/package_run_dependencies/orbbec_camera
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/ament_index/resource_index/parent_prefix_path/orbbec_camera
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/environment/ament_prefix_path.sh
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/environment/ament_prefix_path.dsv
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/environment/path.sh
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/environment/path.dsv
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/local_setup.bash
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/local_setup.sh
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/local_setup.zsh
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/local_setup.dsv
[0.300s] -- Installing: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.dsv
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/ament_index/resource_index/packages/orbbec_camera
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/ament_index/resource_index/rclcpp_components/orbbec_camera
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/cmake/ament_cmake_export_include_directories-extras.cmake
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/cmake/ament_cmake_export_libraries-extras.cmake
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/cmake/ament_cmake_export_dependencies-extras.cmake
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/cmake/orbbec_cameraConfig.cmake
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/cmake/orbbec_cameraConfig-version.cmake
[0.300s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_camera/share/orbbec_camera/package.xml
[0.302s] Invoked command in '/home/<USER>/ws_ros2/build/orbbec_camera' returned '0': CMAKE_PREFIX_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller:/home/<USER>/ws_ros2/install/ymbot_d_control:/home/<USER>/ws_ros2/install/ymbot_hardware_driver:/home/<USER>/ws_ros2/install/ymbot_d_moveit_config:/home/<USER>/ws_ros2/install/vr_pose_tracking_singularity_recovery:/home/<USER>/ws_ros2/install/udp2joint:/home/<USER>/ws_ros2/install/realsense2_description:/home/<USER>/ws_ros2/install/realsense2_camera:/home/<USER>/ws_ros2/install/realsense2_camera_msgs:/home/<USER>/ws_ros2/install/orbbec_description:/home/<USER>/ws_ros2/install/orbbec_camera:/home/<USER>/ws_moveit/install/robotiq_hardware_tests:/home/<USER>/ws_moveit/install/robotiq_driver:/home/<USER>/ws_moveit/install/serial:/home/<USER>/ws_moveit/install/moveit2_tutorials:/home/<USER>/ws_moveit/install/moveit_visual_tools:/home/<USER>/ws_moveit/install/rviz_visual_tools:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities:/home/<USER>/ws_moveit/install/moveit_task_constructor_core:/home/<USER>/ws_moveit/install/rviz_marker_tools:/home/<USER>/ws_moveit/install/kortex_bringup:/home/<USER>/ws_moveit/install/kinova_gen3_lite_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_7dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kinova_gen3_6dof_robotiq_2f_85_moveit_config:/home/<USER>/ws_moveit/install/kortex_description:/home/<USER>/ws_moveit/install/robotiq_description:/home/<USER>/ws_moveit/install/robotiq_controllers:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils:/home/<USER>/ws_moveit/install/moveit_runtime:/home/<USER>/ws_moveit/install/moveit:/home/<USER>/ws_moveit/install/moveit_planners:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner:/home/<USER>/ws_moveit/install/picknik_twist_controller:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller:/home/<USER>/ws_moveit/install/pick_ik:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs:/home/<USER>/ws_moveit/install/moveit_ros_control_interface:/home/<USER>/ws_moveit/install/moveit_plugins:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager:/home/<USER>/ws_moveit/install/moveit_setup_assistant:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins:/home/<USER>/ws_moveit/install/moveit_setup_controllers:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins:/home/<USER>/ws_moveit/install/moveit_setup_framework:/home/<USER>/ws_moveit/install/moveit_servo:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache:/home/<USER>/ws_moveit/install/moveit_ros:/home/<USER>/ws_moveit/install/moveit_ros_visualization:/home/<USER>/ws_moveit/install/moveit_py:/home/<USER>/ws_moveit/install/moveit_hybrid_planning:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks:/home/<USER>/ws_moveit/install/moveit_ros_warehouse:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction:/home/<USER>/ws_moveit/install/moveit_ros_perception:/home/<USER>/ws_moveit/install/moveit_resources_prbt_pg70_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_moveit_config:/home/<USER>/ws_moveit/install/moveit_ros_move_group:/home/<USER>/ws_moveit/install/moveit_planners_ompl:/home/<USER>/ws_moveit/install/moveit_kinematics:/home/<USER>/ws_moveit/install/moveit_ros_planning:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor:/home/<USER>/ws_moveit/install/moveit_resources_prbt_support:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/ws_moveit/install/moveit_resources:/home/<USER>/ws_moveit/install/moveit_resources_pr2_description:/home/<USER>/ws_moveit/install/moveit_resources_panda_moveit_config:/home/<USER>/ws_moveit/install/dual_arm_panda_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_panda_description:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_moveit_config:/home/<USER>/ws_moveit/install/moveit_resources_fanuc_description:/home/<USER>/ws_moveit/install/moveit_planners_stomp:/home/<USER>/ws_moveit/install/moveit_planners_chomp:/home/<USER>/ws_moveit/install/chomp_motion_planner:/home/<USER>/ws_moveit/install/moveit_core:/home/<USER>/ws_moveit/install/moveit_msgs:/home/<USER>/ws_moveit/install/moveit_common:/home/<USER>/ws_moveit/install/kortex_driver:/home/<USER>/ws_moveit/install/kortex_api:/opt/ros/jazzy/opt/gz_sim_vendor:/opt/ros/jazzy/opt/gz_sensors_vendor:/opt/ros/jazzy/opt/gz_physics_vendor:/opt/ros/jazzy/opt/sdformat_vendor:/opt/ros/jazzy/opt/gz_gui_vendor:/opt/ros/jazzy/opt/gz_transport_vendor:/opt/ros/jazzy/opt/gz_rendering_vendor:/opt/ros/jazzy/opt/gz_plugin_vendor:/opt/ros/jazzy/opt/gz_fuel_tools_vendor:/opt/ros/jazzy/opt/gz_msgs_vendor:/opt/ros/jazzy/opt/gz_common_vendor:/opt/ros/jazzy/opt/gz_math_vendor:/opt/ros/jazzy/opt/gz_utils_vendor:/opt/ros/jazzy/opt/gz_tools_vendor:/opt/ros/jazzy/opt/gz_ogre_next_vendor:/opt/ros/jazzy/opt/gz_dartsim_vendor:/opt/ros/jazzy/opt/gz_cmake_vendor:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com LD_LIBRARY_PATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib:/home/<USER>/ws_ros2/install/ymbot_d_ros2_controller/lib:/home/<USER>/ws_ros2/install/ymbot_hardware_driver/lib:/home/<USER>/ws_ros2/install/realsense2_camera/lib:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib:/home/<USER>/ws_ros2/install/orbbec_camera/lib:/home/<USER>/ws_moveit/install/robotiq_driver/lib:/home/<USER>/ws_moveit/install/serial/lib:/home/<USER>/ws_moveit/install/moveit_visual_tools/lib:/home/<USER>/ws_moveit/install/rviz_visual_tools/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_visualization/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_demo/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_capabilities/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib:/home/<USER>/ws_moveit/install/rviz_marker_tools/lib:/home/<USER>/ws_moveit/install/robotiq_controllers/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/ws_moveit/install/pilz_industrial_motion_planner/lib:/home/<USER>/ws_moveit/install/picknik_twist_controller/lib:/home/<USER>/ws_moveit/install/picknik_reset_fault_controller/lib:/home/<USER>/ws_moveit/install/pick_ik/lib:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib:/home/<USER>/ws_moveit/install/moveit_ros_control_interface/lib:/home/<USER>/ws_moveit/install/moveit_simple_controller_manager/lib:/home/<USER>/ws_moveit/install/moveit_setup_assistant/lib:/home/<USER>/ws_moveit/install/moveit_setup_srdf_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_core_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_controllers/lib:/home/<USER>/ws_moveit/install/moveit_setup_app_plugins/lib:/home/<USER>/ws_moveit/install/moveit_setup_framework/lib:/home/<USER>/ws_moveit/install/moveit_servo/lib:/home/<USER>/ws_moveit/install/moveit_ros_trajectory_cache/lib:/home/<USER>/ws_moveit/install/moveit_ros_visualization/lib:/home/<USER>/ws_moveit/install/moveit_py/lib:/home/<USER>/ws_moveit/install/moveit_hybrid_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning_interface/lib:/home/<USER>/ws_moveit/install/moveit_ros_benchmarks/lib:/home/<USER>/ws_moveit/install/moveit_ros_warehouse/lib:/home/<USER>/ws_moveit/install/moveit_ros_robot_interaction/lib:/home/<USER>/ws_moveit/install/moveit_ros_perception/lib:/home/<USER>/ws_moveit/install/moveit_ros_move_group/lib:/home/<USER>/ws_moveit/install/moveit_planners_ompl/lib:/home/<USER>/ws_moveit/install/moveit_kinematics/lib:/home/<USER>/ws_moveit/install/moveit_ros_planning/lib:/home/<USER>/ws_moveit/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/ws_moveit/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/ws_moveit/install/moveit_planners_stomp/lib:/home/<USER>/ws_moveit/install/moveit_planners_chomp/lib:/home/<USER>/ws_moveit/install/chomp_motion_planner/lib:/home/<USER>/ws_moveit/install/moveit_core/lib:/home/<USER>/ws_moveit/install/moveit_msgs/lib:/home/<USER>/ws_moveit/install/kortex_driver/lib:/opt/ros/jazzy/opt/gz_sim_vendor/lib:/opt/ros/jazzy/opt/gz_sensors_vendor/lib:/opt/ros/jazzy/opt/gz_physics_vendor/lib:/opt/ros/jazzy/opt/sdformat_vendor/lib:/opt/ros/jazzy/opt/rviz_ogre_vendor/lib:/opt/ros/jazzy/lib/x86_64-linux-gnu:/opt/ros/jazzy/opt/gz_gui_vendor/lib:/opt/ros/jazzy/opt/gz_transport_vendor/lib:/opt/ros/jazzy/opt/gz_rendering_vendor/lib:/opt/ros/jazzy/opt/gz_plugin_vendor/lib:/opt/ros/jazzy/opt/gz_fuel_tools_vendor/lib:/opt/ros/jazzy/opt/gz_msgs_vendor/lib:/opt/ros/jazzy/opt/gz_common_vendor/lib:/opt/ros/jazzy/opt/gz_math_vendor/lib:/opt/ros/jazzy/opt/gz_utils_vendor/lib:/opt/ros/jazzy/opt/gz_tools_vendor/lib:/opt/ros/jazzy/opt/gz_ogre_next_vendor/lib:/opt/ros/jazzy/opt/gz_dartsim_vendor/lib:/opt/ros/jazzy/opt/gz_cmake_vendor/lib:/opt/ros/jazzy/lib PYTHONPATH=/home/<USER>/ws_ros2/install/orbbec_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/ymbot_d_description/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/vr_receiver/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/realsense2_camera_msgs/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/inspire_hand/lib/python3.12/site-packages:/home/<USER>/ws_ros2/install/dataset_recorder/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_core/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_task_constructor_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_py/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_msgs/lib/python3.12/site-packages:/home/<USER>/ws_moveit/install/moveit_configs_utils/lib/python3.12/site-packages:/opt/ros/jazzy/lib/python3.12/site-packages /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_camera
