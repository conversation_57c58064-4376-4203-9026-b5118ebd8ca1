[0.612s] Invoking command in '/home/<USER>/ws_ros2/build/orbbec_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_description -- -j20 -l20
[0.892s] Invoked command in '/home/<USER>/ws_ros2/build/orbbec_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_description -- -j20 -l20
[0.894s] Invoking command in '/home/<USER>/ws_ros2/build/orbbec_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_description
[0.931s] -- Install configuration: ""
[0.931s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/launch
[0.931s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/launch/launch_utils.py
[0.932s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/launch/view_model.launch.py
[0.932s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes
[0.932s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/femto_bolt
[0.933s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/femto_bolt/femto_bolt.stl
[0.933s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336
[0.933s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_color_optical_frame.STL
[0.933s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_color_frame.STL
[0.933s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_depth_frame.STL
[0.933s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_infra2_optical_frame.STL
[0.933s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_depth_optical_frame.STL
[0.934s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_infra_1_frame.STL
[0.934s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_infra_1_optical_frame.STL
[0.934s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_infra2_frame.STL
[0.934s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_bottom_screw_frame.STL
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/gemini335_336/camera_link.STL
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/meshes/AstraPlus.stl
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/urdf
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/urdf/test_gemini_335.urdf.xarco
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/urdf/test_gemini_336.urdf.xarco
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/urdf/_gemini_335_336.urdf.xacro
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/urdf/femto_bolt.urdf.xacro
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/rviz
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/rviz/urdf.rviz
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/ament_index/resource_index/package_run_dependencies/orbbec_description
[0.935s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/ament_index/resource_index/parent_prefix_path/orbbec_description
[0.936s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/environment/ament_prefix_path.sh
[0.936s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/environment/ament_prefix_path.dsv
[0.936s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/environment/path.sh
[0.936s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/environment/path.dsv
[0.936s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/local_setup.bash
[0.936s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/local_setup.sh
[0.936s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/local_setup.zsh
[0.940s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/local_setup.dsv
[0.941s] -- Installing: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.dsv
[0.941s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/ament_index/resource_index/packages/orbbec_description
[0.941s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/cmake/orbbec_descriptionConfig.cmake
[0.941s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/cmake/orbbec_descriptionConfig-version.cmake
[0.941s] -- Up-to-date: /home/<USER>/ws_ros2/install/orbbec_description/share/orbbec_description/package.xml
[1.009s] Invoked command in '/home/<USER>/ws_ros2/build/orbbec_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_description
