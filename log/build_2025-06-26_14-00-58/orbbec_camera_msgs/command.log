Invoking command in '/home/<USER>/ws_ros2/build/orbbec_camera_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_camera_msgs -- -j20 -l20
Invoked command in '/home/<USER>/ws_ros2/build/orbbec_camera_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --build /home/<USER>/ws_ros2/build/orbbec_camera_msgs -- -j20 -l20
Invoking command in '/home/<USER>/ws_ros2/build/orbbec_camera_msgs': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_camera_msgs
Invoked command in '/home/<USER>/ws_ros2/build/orbbec_camera_msgs' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/ws_ros2/install/ymbot_d_description:/home/<USER>/ws_ros2/install/vr_receiver:/home/<USER>/ws_ros2/install/inspire_hand:/home/<USER>/ws_ros2/install/dataset_recorder:/home/<USER>/ws_moveit/install/moveit_configs_utils:/opt/ros/jazzy DEBUGINFOD_URLS=https://debuginfod.ubuntu.com /usr/bin/cmake --install /home/<USER>/ws_ros2/build/orbbec_camera_msgs
